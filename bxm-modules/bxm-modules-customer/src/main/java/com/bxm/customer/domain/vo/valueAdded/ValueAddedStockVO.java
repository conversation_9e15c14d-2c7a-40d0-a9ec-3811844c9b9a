package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.customer.domain.ValueAddedStock;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * 增值库存VO对象
 *
 * 用于库存数据的展示和传输，提供完整的数据验证和转换功能
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值库存VO对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ValueAddedStockVO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 增值交付单编号 */
    @NotBlank(message = "增值交付单编号不能为空")
    @ApiModelProperty(value = "增值交付单编号", required = true)
    private String deliveryOrderNo;

    /** 账期 */
    @NotBlank(message = "账期不能为空")
    @ApiModelProperty(value = "账期", required = true)
    private String period;

    /** 解析结果，1-正常，2-异常 */
    @NotNull(message = "解析结果不能为空")
    @ApiModelProperty(value = "解析结果：1-正常，2-异常", required = true, allowableValues = "1,2")
    private Integer analysisResult;

    /** 解析结果描述 */
    @ApiModelProperty(value = "解析结果描述")
    private String analysisResultName;

    /** 库存数 */
    @ApiModelProperty(value = "库存数")
    private Integer stockCount;

    /** 备注 */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 从 ValueAddedStock 实体转换为 VO
     *
     * @param stock 库存实体
     * @return VO对象
     */
    public static ValueAddedStockVO fromEntity(ValueAddedStock stock) {
        if (stock == null) {
            return null;
        }

        ValueAddedStockVO vo = new ValueAddedStockVO();
        BeanUtils.copyProperties(stock, vo);
        vo.setAnalysisResultName(getAnalysisResultName(stock.getAnalysisResult()));
        return vo;
    }

    /**
     * 批量转换实体列表为VO列表
     *
     * @param stockList 库存实体列表
     * @return VO列表
     */
    public static List<ValueAddedStockVO> fromEntityList(List<ValueAddedStock> stockList) {
        if (stockList == null || stockList.isEmpty()) {
            return new ArrayList<>();
        }

        return stockList.stream()
                .map(ValueAddedStockVO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取解析结果描述
     *
     * @param analysisResult 解析结果代码
     * @return 解析结果描述
     */
    private static String getAnalysisResultName(Integer analysisResult) {
        if (analysisResult == null) {
            return null;
        }

        switch (analysisResult) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            default:
                return "未知";
        }
    }
}
