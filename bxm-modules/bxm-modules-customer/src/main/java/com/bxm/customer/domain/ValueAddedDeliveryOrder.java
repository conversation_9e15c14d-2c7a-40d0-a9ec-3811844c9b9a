package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.handler.AccountingInfoTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.AccountingInfoVO;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 增值交付单对象 c_value_added_delivery_order
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel("增值交付单对象")
@Accessors(chain = true)
@TableName(value = "c_value_added_delivery_order", autoResultMap = true)
public class ValueAddedDeliveryOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 交付单编号 */
    @Excel(name = "交付单编号")
    @TableField("delivery_order_no")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /** 交付单标题 */
    @Excel(name = "交付单标题")
    @TableField("title")
    @ApiModelProperty(value = "交付单标题")
    private String title;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 客户企业名称 */
    @Excel(name = "客户企业名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户企业名称")
    private String customerName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_no")
    @ApiModelProperty(value = "税号")
    private String taxNo;

    /** 纳税性质，1-小规模纳税人，2-一般纳税人 */
    @Excel(name = "纳税性质")
    @TableField("taxpayer_type")
    @ApiModelProperty(value = "纳税性质，1-小规模纳税人，2-一般纳税人")
    private Integer taxpayerType;

    /** 增值事项 */
    @Excel(name = "增值事项")
    @TableField("value_added_item_type_id")
    @ApiModelProperty(value = "增值事项")
    private Integer valueAddedItemTypeId;

    /** 账期开始时间 (格式：YYYYMM，如：202301) */
    @Excel(name = "账期开始时间")
    @TableField("accounting_period_start")
    @ApiModelProperty(value = "账期开始时间，格式YYYYMM")
    private Integer accountingPeriodStart;

    /** 账期结束时间 (格式：YYYYMM，如：202310) */
    @Excel(name = "账期结束时间")
    @TableField("accounting_period_end")
    @ApiModelProperty(value = "账期结束时间，格式YYYYMM")
    private Integer accountingPeriodEnd;



    /** 联络人手机号 */
    @Excel(name = "联络人手机号")
    @TableField("contact_mobile")
    @ApiModelProperty(value = "联络人手机号")
    private String contactMobile;

    /** 联络人证件号 */
    @Excel(name = "联络人证件号")
    @TableField("contact_id_number")
    @ApiModelProperty(value = "联络人证件号")
    private String contactIdNumber;

    /** 是否同步手续费，0-否，1-是 */
    @Excel(name = "是否同步手续费")
    @TableField("sync_handling_fee")
    @ApiModelProperty(value = "是否同步手续费，0-否，1-是")
    private Boolean syncHandlingFee;

    /** 账户类型信息 (以JSON格式存储在数据库中) */
    @Excel(name = "账户类型")
    @TableField(value = "account_types", typeHandler = AccountingInfoTypeHandler.class)
    @ApiModelProperty(value = "账户类型信息")
    private AccountingInfoVO accountingInfo;

    /** 交付要求 */
    @Excel(name = "交付要求")
    @TableField("requirements")
    @ApiModelProperty(value = "交付要求")
    private String requirements;

    /** 税负要求 */
    @Excel(name = "税负要求")
    @TableField("tax_requirement")
    @ApiModelProperty(value = "税负要求")
    private String taxRequirement;

    /** 是否同步改派，0-否，1-是 */
    @Excel(name = "是否同步改派")
    @TableField("sync_reassignment")
    @ApiModelProperty(value = "是否同步改派，0-否，1-是")
    private Boolean syncReassignment;

    /** 是否同步联络员，0-否，1-是 */
    @Excel(name = "是否同步联络员")
    @TableField("sync_contact_person")
    @ApiModelProperty(value = "是否同步联络员，0-否，1-是")
    private Boolean syncContactPerson;

    /** 是否同步改账，0-否，1-是 */
    @Excel(name = "是否同步改账")
    @TableField("sync_account_change")
    @ApiModelProperty(value = "是否同步改账，0-否，1-是")
    private Boolean syncAccountChange;

    /** 是否修改工期，0-否，1-是 */
    @Excel(name = "是否修改工期")
    @TableField("modify_due_date")
    @ApiModelProperty(value = "是否修改工期，0-否，1-是")
    private Boolean modifyDueDate;

    /** 交付截止日期 */
    @Excel(name = "交付截止日期")
    @TableField("ddl")
    @ApiModelProperty(value = "交付截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    /** 发起部门ID */
    @Excel(name = "发起部门ID")
    @TableField("initiate_dept_id")
    @ApiModelProperty(value = "发起部门ID")
    private Long initiateDeptId;

    /** 会计区域ID */
    @Excel(name = "会计区域ID")
    @TableField("accounting_top_dept_id")
    @ApiModelProperty(value = "会计区域ID")
    private Long accountingTopDeptId;

    /** 会计部门ID */
    @Excel(name = "会计部门ID")
    @TableField("accounting_dept_id")
    @ApiModelProperty(value = "会计部门ID")
    private Long accountingDeptId;

    /** 交付状态 */
    @Excel(name = "交付状态")
    @TableField("status")
    @ApiModelProperty(value = "交付状态")
    private String status;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 业务部门id */
    @Excel(name = "业务部门id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @Excel(name = "顶级业务部门id")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 总扣缴额 */
    @Excel(name = "总扣缴额")
    @TableField("total_withholding_amount")
    @ApiModelProperty(value = "总扣缴额")
    private BigDecimal totalWithholdingAmount;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    @TableField("create_uid")
    @ApiModelProperty(value = "创建人ID")
    private Long createUid;

    /** 操作备注 */
    @Excel(name = "操作备注")
    @TableField("remark")
    @ApiModelProperty(value = "操作备注")
    private String remark;

    /** 交付备注 */
    @Excel(name = "交付备注")
    @TableField("delivery_remark")
    @ApiModelProperty(value = "交付备注")
    private String deliveryRemark;

    /** 发起时间 */
    @Excel(name = "发起时间")
    @TableField("initiate_time")
    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date initiateTime;

    /** 会计承接时间 */
    @Excel(name = "会计承接时间")
    @TableField("accounting_create_time")
    @ApiModelProperty(value = "会计承接时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accountingCreateTime;


}
