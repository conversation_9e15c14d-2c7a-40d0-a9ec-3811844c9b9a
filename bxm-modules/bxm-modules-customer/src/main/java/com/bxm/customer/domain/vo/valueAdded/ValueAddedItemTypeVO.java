package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增值事项类型VO
 *
 * 用于前端展示增值事项类型信息
 *
 * <AUTHOR>
 * @date 2025-08-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值事项类型VO")
public class ValueAddedItemTypeVO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 事项名称 */
    @ApiModelProperty(value = "事项名称")
    private String itemName;

    /** 事项类型 */
    @ApiModelProperty(value = "事项类型")
    private String itemType;

    /** 事项类型编码 */
    @ApiModelProperty(value = "事项类型编码")
    private String itemTypeCode;

    /** 事项编码 */
    @ApiModelProperty(value = "事项编码")
    private String itemCode;

    /** 处理类型 */
    @ApiModelProperty(value = "处理类型")
    private Integer processType;
}
