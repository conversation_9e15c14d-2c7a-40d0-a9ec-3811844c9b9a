package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 增值事项类型对象 c_value_added_item_type
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel("增值事项类型对象")
@Accessors(chain = true)
@TableName(value = "c_value_added_item_type", autoResultMap = true)
public class ValueAddedItemType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 事项名称 */
    @Excel(name = "事项名称")
    @TableField("item_name")
    @ApiModelProperty(value = "事项名称")
    private String itemName;

    /** 事项类型 */
    @Excel(name = "事项类型")
    @TableField("item_type")
    @ApiModelProperty(value = "事项类型")
    private String itemType;

    /** 事项类型编码 */
    @Excel(name = "事项类型编码")
    @TableField("item_type_code")
    @ApiModelProperty(value = "事项类型编码")
    private String itemTypeCode;

    /** 事项编码 */
    @Excel(name = "事项编码")
    @TableField("item_code")
    @ApiModelProperty(value = "事项编码")
    private String itemCode;

    /** 处理类型 */
    @Excel(name = "处理类型")
    @TableField("process_type")
    @ApiModelProperty(value = "处理类型")
    private Integer processType;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除", readConverterExp = "0=否,1=是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;
}
